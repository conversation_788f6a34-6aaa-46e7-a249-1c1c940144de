# <PERSON> - Linux Kernel Engineer Portfolio

[![Node.js Version](https://img.shields.io/badge/node-%3E%3D18.0.0-brightgreen.svg)](https://nodejs.org/)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Portfolio Status](https://img.shields.io/badge/status-active-success.svg)](http://localhost:3000)

A professional portfolio website for <PERSON>, showcasing expertise in Linux Kernel Engineering, C/Rust programming, cloud infrastructure, and cybersecurity. Built with modern web technologies and featuring a comprehensive design system, dark mode support, and full accessibility compliance.

## 🚀 Features

- **Professional Design System** - Cohesive black & white aesthetic with Linux Kernel Engineer branding
- **Dark Mode Support** - Complete dark theme with localStorage persistence and smooth transitions
- **Responsive Design** - Mobile-first approach optimized for all device sizes
- **Accessibility Compliant** - WCAG 2.1 AA standards with 44px minimum touch targets
- **Performance Optimized** - Compression, caching, and optimized assets
- **SEO Ready** - Comprehensive meta tags and structured data
- **Security Enhanced** - Helmet.js, CORS, CSP, and security headers
- **API Endpoints** - RESTful API for portfolio data and health monitoring

## 📋 Table of Contents

- [System Requirements](#system-requirements)
- [Quick Start](#quick-start)
- [Installation](#installation)
- [Development](#development)
- [Production](#production)
- [API Documentation](#api-documentation)
- [Design System](#design-system)
- [File Structure](#file-structure)
- [Testing](#testing)
- [Troubleshooting](#troubleshooting)

## 🔧 System Requirements

### Required Software
- **Node.js**: >= 18.0.0 (LTS recommended)
- **npm**: >= 8.0.0 (comes with Node.js)
- **Git**: Latest version

### Browser Compatibility
- **Chrome**: >= 90
- **Firefox**: >= 88
- **Safari**: >= 14
- **Edge**: >= 90
- **Mobile browsers**: iOS Safari >= 14, Chrome Mobile >= 90

### Operating System
- **Linux**: Ubuntu 20.04+, CentOS 8+, Debian 11+
- **macOS**: 10.15+
- **Windows**: 10+ (with WSL2 recommended)

## ⚡ Quick Start

```bash
# Clone the repository
git clone <repository-url>
cd Black-And-White-master

# Install dependencies
npm install

# Start development server
npm run dev

# Open browser to http://localhost:3000
```

## 📦 Installation

### 1. Clone Repository
```bash
git clone <repository-url>
cd Black-And-White-master
```

### 2. Install Dependencies
```bash
# Install all required packages
npm install

# Verify installation
npm list --depth=0
```

### 3. Environment Setup
```bash
# Copy environment template (if exists)
cp .env.example .env

# Edit environment variables (optional)
nano .env
```

### 4. Verify Installation
```bash
# Run health check
npm run test

# Start server
npm run dev
```

## 🛠️ Development

### Available Scripts

```bash
# Development server with hot reload
npm run dev

# Production server
npm start

# Run linting
npm run lint

# Fix linting issues automatically
npm run lint:fix

# Validate HTML
npm run validate

# Optimize assets
npm run optimize

# Run all tests
npm run test

# Clean build artifacts
npm run clean
```

### Development Server

The development server includes:
- **Hot reload** for CSS and JavaScript changes
- **Automatic browser refresh** on file changes
- **Development logging** for debugging
- **Source maps** for easier debugging
- **Error overlay** for quick issue identification

```bash
# Start development server
npm run dev

# Server will be available at:
# http://localhost:3000
```

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `NODE_ENV` | `development` | Environment mode |
| `PORT` | `3000` | Server port |
| `HOST` | `localhost` | Server host |

## 🚀 Production

### Build for Production

```bash
# Optimize all assets
npm run optimize

# Start production server
npm start
```

### Production Features

- **Asset compression** (Gzip/Brotli)
- **Caching headers** for static assets
- **Security headers** (CSP, HSTS, etc.)
- **Performance monitoring**
- **Error logging**

### Deployment Options

#### Option 1: Traditional Server
```bash
# On your server
git clone <repository-url>
cd Black-And-White-master
npm install --production
npm run optimize
npm start
```

#### Option 2: Docker (if Dockerfile exists)
```bash
docker build -t daniel-orji-portfolio .
docker run -p 3000:3000 daniel-orji-portfolio
```

#### Option 3: Process Manager (PM2)
```bash
npm install -g pm2
pm2 start server.js --name "daniel-portfolio"
pm2 startup
pm2 save
```

## 📡 API Documentation

### Health Check
```http
GET /api/health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0",
  "environment": "development"
}
```

### Portfolio Statistics
```http
GET /api/portfolio/stats
```

**Response:**
```json
{
  "projects": 12,
  "technologies": ["C", "Rust", "Python", "Linux", "Docker"],
  "experience": "5+ years",
  "specialization": "Linux Kernel Engineering"
}
```

### Contact Form (if implemented)
```http
POST /api/contact
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "message": "Hello Daniel!"
}
```

## 🎨 Design System

The portfolio uses a comprehensive design system extracted from the main template:

### Core Principles
- **Professional black & white aesthetic**
- **Linux Kernel Engineer branding**
- **Accessibility-first approach**
- **Mobile-first responsive design**
- **Dark mode support**

### Key Files
- `css/design-system.css` - Core design system
- `DESIGN_SYSTEM.md` - Comprehensive documentation
- `page-template.html` - Template for new pages

### Usage Example
```html
<!-- Hero Section -->
<section class="hero-section">
  <h1 class="hero-title">Page Title</h1>
  <h2 class="hero-subtitle">Subtitle</h2>
  <div class="hero-actions">
    <a href="#" class="btn-primary">Primary Action</a>
    <a href="#" class="btn-secondary">Secondary Action</a>
  </div>
</section>

<!-- Project Cards -->
<div class="grid-3">
  <div class="card-project">
    <h3 class="card-title">Project Title</h3>
    <p class="card-description">Description</p>
    <div class="tech-badges">
      <span class="badge-tech linux">Linux</span>
      <span class="badge-tech rust">Rust</span>
    </div>
  </div>
</div>
```

## 📁 File Structure

```
Black-And-White-master/
├── 📄 README.md                 # This file
├── 📄 package.json             # Dependencies and scripts
├── 📄 server.js                # Express.js server
├── 📄 .eslintrc.js             # ESLint configuration
├── 📄 DESIGN_SYSTEM.md         # Design system documentation
├── 📄 INSTALLATION.md          # Detailed installation guide
├── 📄 page-template.html       # Template for new pages
├── 📁 css/
│   ├── 📄 design-system.css    # Core design system
│   ├── 📄 custom.css           # Original theme styles
│   ├── 📄 portfolio.css        # Portfolio enhancements
│   └── 📄 bootstrap.min.css    # Bootstrap framework
├── 📁 js/
│   ├── 📄 portfolio.js         # Portfolio functionality & dark mode
│   └── 📄 script.js           # Original theme scripts
├── 📁 scripts/
│   └── 📄 optimize.js          # Asset optimization
├── 📁 images/                  # Image assets
├── 📁 fonts/                   # Font files
├── 📄 index.html              # Homepage
├── 📄 about.html              # About page
├── 📄 projects.html           # Projects page
├── 📄 contact.html            # Contact page
└── 📄 support.html            # Support page
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm test

# Run individual test suites
npm run lint          # Code quality
npm run validate      # HTML validation
npm run test:api      # API endpoint tests
npm run test:e2e      # End-to-end tests (if available)
```

### Manual Testing Checklist

#### Functionality
- [ ] All pages load correctly (/, /about, /projects, /contact, /support)
- [ ] Navigation works between all pages
- [ ] Dark mode toggle functions properly
- [ ] Theme preference persists across sessions
- [ ] API endpoints return correct data
- [ ] Contact form submits successfully (if implemented)

#### Responsive Design
- [ ] Mobile layout (< 768px)
- [ ] Tablet layout (768px - 1023px)
- [ ] Desktop layout (1024px+)
- [ ] Touch targets are minimum 44px
- [ ] Text is readable at all sizes

#### Accessibility
- [ ] Keyboard navigation works
- [ ] Screen reader compatibility
- [ ] High contrast mode support
- [ ] Focus indicators visible
- [ ] Skip navigation links function

#### Performance
- [ ] Page load times < 3 seconds
- [ ] Images are optimized
- [ ] CSS/JS is minified in production
- [ ] Caching headers are set correctly

## 🔧 Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Find process using port 3000
lsof -i :3000

# Kill the process
kill -9 <PID>

# Or use a different port
PORT=3001 npm run dev
```

#### Node.js Version Issues
```bash
# Check current version
node --version

# Install Node Version Manager (nvm)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# Install and use Node.js 18
nvm install 18
nvm use 18
```

#### Permission Errors
```bash
# Fix npm permissions (Linux/macOS)
sudo chown -R $(whoami) ~/.npm

# Or use npx instead of global installs
npx <package-name>
```

#### Build Failures
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

### Getting Help

1. **Check the logs** - Look at console output for error messages
2. **Verify system requirements** - Ensure Node.js version compatibility
3. **Check file permissions** - Ensure read/write access to project files
4. **Review environment variables** - Verify all required variables are set
5. **Test in isolation** - Try running individual components

### Debug Mode

```bash
# Enable debug logging
DEBUG=* npm run dev

# Enable Node.js debugging
node --inspect server.js
```

## 🤝 Contributing

### Development Workflow

1. **Fork the repository**
2. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```
3. **Make your changes**
4. **Run tests**
   ```bash
   npm test
   ```
5. **Commit your changes**
   ```bash
   git commit -m "Add your feature description"
   ```
6. **Push to your fork**
   ```bash
   git push origin feature/your-feature-name
   ```
7. **Create a Pull Request**

### Code Standards

- **ESLint** - Follow the configured linting rules
- **HTML Validation** - Ensure valid HTML5
- **Accessibility** - Maintain WCAG 2.1 AA compliance
- **Performance** - Optimize for speed and efficiency
- **Documentation** - Update docs for any changes

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 📞 Contact

**Daniel Orji** - Aspiring Linux Kernel Engineer
- Portfolio: [http://localhost:3000](http://localhost:3000)
- Email: [contact information]
- LinkedIn: [LinkedIn profile]
- GitHub: [GitHub profile]

---

*Built with ❤️ for the Linux and Open Source community*
