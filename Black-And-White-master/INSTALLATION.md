# Installation Guide - <PERSON>

This comprehensive guide will walk you through setting up the Daniel Orji Linux Kernel Engineer portfolio application on your local machine.

## 📋 Prerequisites

### System Requirements

#### Operating System
- **Linux**: Ubuntu 20.04+, CentOS 8+, Debian 11+, Fedora 34+
- **macOS**: 10.15 (Catalina) or later
- **Windows**: Windows 10+ with WSL2 (recommended) or native Windows

#### Required Software
- **Node.js**: Version 18.0.0 or higher (LTS recommended)
- **npm**: Version 8.0.0 or higher (comes with Node.js)
- **Git**: Latest stable version
- **Text Editor**: VS Code, Sublime Text, or similar

#### Hardware Requirements
- **RAM**: Minimum 4GB, recommended 8GB+
- **Storage**: At least 1GB free space
- **Network**: Internet connection for downloading dependencies

### Browser Requirements
- **Chrome**: Version 90+
- **Firefox**: Version 88+
- **Safari**: Version 14+
- **Edge**: Version 90+

## 🔧 Step-by-Step Installation

### Step 1: Install Node.js

#### Option A: Official Installer (Recommended)
1. Visit [nodejs.org](https://nodejs.org/)
2. Download the LTS version for your operating system
3. Run the installer and follow the prompts
4. Verify installation:
   ```bash
   node --version  # Should show v18.x.x or higher
   npm --version   # Should show 8.x.x or higher
   ```

#### Option B: Using Node Version Manager (Advanced)
```bash
# Install nvm (Linux/macOS)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# Restart terminal or source profile
source ~/.bashrc

# Install and use Node.js 18
nvm install 18
nvm use 18
nvm alias default 18
```

#### Option C: Package Manager
```bash
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# CentOS/RHEL/Fedora
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo dnf install nodejs npm

# macOS with Homebrew
brew install node

# Windows with Chocolatey
choco install nodejs
```

### Step 2: Install Git

#### Linux
```bash
# Ubuntu/Debian
sudo apt update && sudo apt install git

# CentOS/RHEL/Fedora
sudo dnf install git

# Arch Linux
sudo pacman -S git
```

#### macOS
```bash
# With Homebrew
brew install git

# Or download from https://git-scm.com/download/mac
```

#### Windows
1. Download from [git-scm.com](https://git-scm.com/download/win)
2. Run installer with default settings
3. Use Git Bash for command line operations

### Step 3: Clone the Repository

```bash
# Navigate to your desired directory
cd ~/Documents  # or wherever you want to store the project

# Clone the repository
git clone <repository-url>

# Navigate to project directory
cd Black-And-White-master

# Verify files are present
ls -la
```

### Step 4: Install Dependencies

```bash
# Install all required packages
npm install

# This will install:
# - Express.js (web server)
# - Helmet.js (security)
# - Compression (performance)
# - ESLint (code quality)
# - HTML Validator (validation)
# - And other dependencies
```

#### Verify Installation
```bash
# Check installed packages
npm list --depth=0

# Should show all dependencies without errors
```

### Step 5: Environment Configuration

#### Create Environment File (Optional)
```bash
# Copy example environment file if it exists
cp .env.example .env

# Or create a new one
touch .env
```

#### Edit Environment Variables
```bash
# Open in your preferred editor
nano .env
# or
code .env
```

#### Common Environment Variables
```env
# Server Configuration
NODE_ENV=development
PORT=3000
HOST=localhost

# Security (optional)
SESSION_SECRET=your-secret-key-here

# API Keys (if needed)
CONTACT_API_KEY=your-api-key
ANALYTICS_ID=your-analytics-id
```

### Step 6: Verify Installation

#### Run Health Check
```bash
# Run the test suite
npm test

# Should show:
# ✓ ESLint checks
# ✓ HTML validation
# ✓ Server startup test
```

#### Start Development Server
```bash
# Start the server
npm run dev

# You should see:
# 🚀 Daniel Orji Portfolio Server
# 📍 Environment: development
# 🌐 Server running on: http://localhost:3000
```

#### Test in Browser
1. Open your browser
2. Navigate to `http://localhost:3000`
3. Verify all pages load:
   - Homepage: `http://localhost:3000/`
   - About: `http://localhost:3000/about`
   - Projects: `http://localhost:3000/projects`
   - Contact: `http://localhost:3000/contact`
   - Support: `http://localhost:3000/support`

#### Test API Endpoints
```bash
# Test health endpoint
curl http://localhost:3000/api/health

# Test portfolio stats
curl http://localhost:3000/api/portfolio/stats
```

## 🔍 Verification Checklist

### ✅ Installation Verification

- [ ] Node.js version 18+ installed
- [ ] npm version 8+ installed
- [ ] Git installed and configured
- [ ] Repository cloned successfully
- [ ] Dependencies installed without errors
- [ ] Environment variables configured (if needed)
- [ ] Development server starts without errors
- [ ] All pages load in browser
- [ ] API endpoints respond correctly
- [ ] Dark mode toggle works
- [ ] Responsive design functions on mobile

### ✅ Feature Testing

- [ ] Navigation between pages works
- [ ] Dark mode persists across sessions
- [ ] Contact form functions (if implemented)
- [ ] Images load correctly
- [ ] CSS animations work smoothly
- [ ] JavaScript functionality works
- [ ] No console errors in browser

## 🚨 Troubleshooting

### Common Installation Issues

#### Issue: Node.js Version Too Old
```bash
# Error: "Node.js version 16.x.x is not supported"
# Solution: Update Node.js
nvm install 18
nvm use 18
```

#### Issue: Permission Denied
```bash
# Error: "EACCES: permission denied"
# Solution: Fix npm permissions
sudo chown -R $(whoami) ~/.npm
```

#### Issue: Port Already in Use
```bash
# Error: "EADDRINUSE: address already in use :::3000"
# Solution: Kill process or use different port
lsof -i :3000
kill -9 <PID>
# or
PORT=3001 npm run dev
```

#### Issue: Module Not Found
```bash
# Error: "Cannot find module 'express'"
# Solution: Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

#### Issue: Git Clone Fails
```bash
# Error: "Repository not found"
# Solution: Check repository URL and permissions
git clone https://github.com/username/repository.git
```

### Platform-Specific Issues

#### Windows Issues
- **Use Git Bash** for command line operations
- **Enable WSL2** for better compatibility
- **Run as Administrator** if permission issues occur
- **Use PowerShell** as alternative to Command Prompt

#### macOS Issues
- **Install Xcode Command Line Tools**: `xcode-select --install`
- **Use Homebrew** for package management
- **Check Gatekeeper** settings for security

#### Linux Issues
- **Update package manager**: `sudo apt update` or `sudo dnf update`
- **Install build tools**: `sudo apt install build-essential`
- **Check firewall** settings for port access

### Performance Issues

#### Slow Installation
```bash
# Use faster npm registry
npm config set registry https://registry.npmjs.org/

# Clear npm cache
npm cache clean --force

# Use yarn as alternative
npm install -g yarn
yarn install
```

#### High Memory Usage
```bash
# Increase Node.js memory limit
node --max-old-space-size=4096 server.js

# Or set environment variable
export NODE_OPTIONS="--max-old-space-size=4096"
```

## 🔧 Advanced Configuration

### Development Tools Setup

#### VS Code Extensions (Recommended)
- ESLint
- Prettier
- HTML CSS Support
- Live Server
- GitLens

#### Browser Extensions
- React Developer Tools (if using React)
- Vue.js devtools (if using Vue)
- Web Developer
- Lighthouse

### Custom Configuration

#### ESLint Configuration
```bash
# Customize linting rules
nano .eslintrc.js
```

#### HTML Validation Rules
```bash
# Customize validation rules
nano .htmlvalidate.json
```

### Production Setup

#### Environment Variables for Production
```env
NODE_ENV=production
PORT=80
HOST=0.0.0.0
```

#### Process Manager (PM2)
```bash
# Install PM2 globally
npm install -g pm2

# Start application with PM2
pm2 start server.js --name "daniel-portfolio"

# Setup auto-restart on boot
pm2 startup
pm2 save
```

## 📞 Getting Help

### Documentation Resources
- [Node.js Documentation](https://nodejs.org/docs/)
- [Express.js Guide](https://expressjs.com/guide/)
- [npm Documentation](https://docs.npmjs.com/)

### Community Support
- [Stack Overflow](https://stackoverflow.com/questions/tagged/node.js)
- [Node.js Community](https://nodejs.org/community/)
- [Express.js Community](https://expressjs.com/community.html)

### Project-Specific Help
1. Check the [README.md](README.md) for general information
2. Review [DESIGN_SYSTEM.md](DESIGN_SYSTEM.md) for design guidelines
3. Look at [IMPLEMENTATION_GUIDE.md](IMPLEMENTATION_GUIDE.md) for technical details
4. Check the issue tracker for known problems
5. Contact the maintainer for specific questions

---

*Installation complete! You're now ready to develop and customize the Daniel Orji Linux Kernel Engineer portfolio.*
