<!DOCTYPE html>
<html lang="en">
<head>
    <title>Dark Mode Validation - <PERSON></title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/ionicons.min.css">
    <link rel="stylesheet" href="css/design-system.css">
    <link rel="stylesheet" href="css/custom.css">
    <link rel="stylesheet" href="css/portfolio.css">
    
    <!-- JS -->
    <script src="js/jquery-2.1.3.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
</head>

<body>
    <!-- Skip Navigation -->
    <a href="#main-content" class="skip-nav">Skip to main content</a>

    <div class="container">
        <header id="site-header" role="banner">
            <div class="row">
                <div class="col-md-4 col-sm-5 col-xs-8">
                    <div class="logo">
                        <h1><a href="index.html"><b>Daniel</b> Orji</a></h1>
                    </div>
                </div>
                <div class="col-md-8 col-sm-7 col-xs-4">
                    <nav class="main-nav" role="navigation">
                        <div class="navbar-header">
                            <button type="button" id="trigger-overlay" class="navbar-toggle">
                                <span class="ion-navicon"></span>
                            </button>
                        </div>
                        <div class="collapse navbar-collapse">
                            <ul class="nav navbar-nav navbar-right">
                                <li class="cl-effect-11"><a href="index.html" data-hover="Home">Home</a></li>
                                <li class="cl-effect-11"><a href="projects.html" data-hover="Projects">Projects</a></li>
                                <li class="cl-effect-11"><a href="about.html" data-hover="About">About</a></li>
                                <li class="cl-effect-11"><a href="contact.html" data-hover="Contact">Contact</a></li>
                                <li class="cl-effect-11"><a href="support.html" data-hover="Support">Support</a></li>
                            </ul>
                        </div>
                    </nav>
                    <div id="header-theme-toggle">
                        <button class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode" title="Toggle dark/light theme">
                            <svg class="theme-toggle-icon sun-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                            </svg>
                            <svg class="theme-toggle-icon moon-icon" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </header>
    </div>

    <!-- Hero Section -->
    <section class="hero-section" role="banner" aria-labelledby="hero-title">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <h1 id="hero-title" class="hero-title">Dark Mode Validation</h1>
                    <h2 class="hero-subtitle">Testing All Components</h2>
                    <p class="hero-description">
                        This page tests all design system components in both light and dark modes to ensure proper theme integration and accessibility compliance.
                    </p>
                    <div class="hero-actions">
                        <a href="#" class="btn-primary" role="button">Primary Button</a>
                        <a href="#" class="btn-secondary" role="button">Secondary Button</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="content-body">
        <div class="container">
            <div class="row">
                <main id="main-content" class="col-md-12" role="main">
                    
                    <!-- Typography Testing -->
                    <section class="content-section">
                        <h2 class="section-title">Typography Testing</h2>
                        
                        <div class="grid-2">
                            <div>
                                <h1 class="heading-1">Heading 1</h1>
                                <h2 class="heading-2">Heading 2</h2>
                                <h3 class="heading-3">Heading 3</h3>
                                <h4 class="heading-4">Heading 4</h4>
                                <p class="body-large">Large body text for important content.</p>
                                <p class="body-normal">Normal body text for regular content.</p>
                                <p class="body-small">Small body text for secondary information.</p>
                                <p class="text-caption">Caption text for labels</p>
                            </div>
                            <div>
                                <h1 class="text-hero">Hero Text</h1>
                                <h2 class="text-subtitle">Subtitle Text</h2>
                                <p class="specialization-description">
                                    Specialization description text that should adapt to theme changes.
                                </p>
                                <p>Regular paragraph text that should be readable in both themes.</p>
                                <a href="#">Link text that changes color</a>
                            </div>
                        </div>
                    </section>

                    <!-- Button Testing -->
                    <section class="content-section alternate">
                        <h2 class="section-title">Button Testing</h2>
                        
                        <div class="grid-3">
                            <div class="card-project">
                                <h3 class="card-title">Design System Buttons</h3>
                                <p class="card-description">
                                    Testing the new design system button classes.
                                </p>
                                <div class="card-actions">
                                    <a href="#" class="btn-primary">Primary Action</a>
                                    <a href="#" class="btn-secondary">Secondary Action</a>
                                </div>
                            </div>

                            <div class="card-project">
                                <h3 class="card-title">Legacy Buttons</h3>
                                <p class="card-description">
                                    Testing the existing portfolio button classes.
                                </p>
                                <div class="card-actions">
                                    <a href="#" class="btn-cta">CTA Button</a>
                                    <a href="#" class="btn-project">Project Button</a>
                                </div>
                            </div>

                            <div class="card-project">
                                <h3 class="card-title">Technology Badges</h3>
                                <p class="card-description">
                                    Testing technology badge components.
                                </p>
                                <div class="tech-badges">
                                    <span class="badge-tech linux">Linux</span>
                                    <span class="badge-tech rust">Rust</span>
                                    <span class="badge-tech cloud">Cloud</span>
                                    <span class="badge-tech">General</span>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Skills Testing -->
                    <section class="content-section">
                        <h2 class="section-title">Skills Components</h2>
                        
                        <div class="grid-skills">
                            <div class="skill-category">
                                <h3>Programming Languages</h3>
                                <div class="skill-item">
                                    <div class="skill-name">
                                        <span>C Programming</span>
                                        <span>90%</span>
                                    </div>
                                    <div class="skill-bar">
                                        <div class="skill-progress c-lang"></div>
                                    </div>
                                </div>
                                <div class="skill-item">
                                    <div class="skill-name">
                                        <span>Rust</span>
                                        <span>65%</span>
                                    </div>
                                    <div class="skill-bar">
                                        <div class="skill-progress rust-lang"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Form Testing -->
                    <section class="content-section alternate">
                        <h2 class="section-title">Form Elements</h2>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <form>
                                    <input type="text" placeholder="Your Name" class="form-control">
                                    <input type="email" placeholder="Your Email" class="form-control">
                                    <textarea placeholder="Your Message" rows="4" class="form-control"></textarea>
                                    <button type="submit" class="btn-primary">Submit Form</button>
                                </form>
                            </div>
                            <div class="col-md-6">
                                <h3>Contrast Testing</h3>
                                <p>This section tests text contrast ratios in both light and dark modes.</p>
                                <ul>
                                    <li>Primary text should have high contrast</li>
                                    <li>Secondary text should be readable</li>
                                    <li>Muted text should be distinguishable</li>
                                    <li>Links should be clearly identifiable</li>
                                </ul>
                            </div>
                        </div>
                    </section>

                    <!-- Call to Action -->
                    <section class="content-section">
                        <div class="text-center">
                            <h2 class="section-title">Theme Toggle Test</h2>
                            <p class="hero-description">
                                Use the theme toggle button in the header to switch between light and dark modes. 
                                All elements should transition smoothly and maintain proper contrast ratios.
                            </p>
                            <a href="index.html" class="btn-primary">Back to Portfolio</a>
                        </div>
                    </section>

                </main>
            </div>
        </div>
    </div>

    <footer id="site-footer">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <p class="copyright">&copy; 2024 Daniel Orji - Linux Kernel Engineer</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Mobile Menu -->
    <div class="overlay overlay-hugeinc" role="dialog" aria-labelledby="mobile-menu-title" aria-hidden="true">
        <button type="button" class="overlay-close" aria-label="Close mobile menu">
            <span class="ion-ios-close-empty" aria-hidden="true"></span>
        </button>
        <nav role="navigation" aria-labelledby="mobile-menu-title">
            <h2 id="mobile-menu-title" class="sr-only">Mobile Navigation Menu</h2>
            <ul>
                <li><a href="index.html">Home</a></li>
                <li><a href="projects.html">Projects</a></li>
                <li><a href="about.html">About</a></li>
                <li><a href="contact.html">Contact</a></li>
                <li><a href="support.html">Support</a></li>
            </ul>
        </nav>
    </div>

    <script src="js/script.js"></script>
    <script src="js/portfolio.js"></script>

</body>
</html>
