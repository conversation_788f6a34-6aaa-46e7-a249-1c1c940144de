# Startup & Development Guide - <PERSON>

This guide covers how to start, develop, and maintain the Daniel Orji Linux Kernel Engineer portfolio application.

## 🚀 Quick Start Commands

```bash
# Start development server
npm run dev

# Start production server
npm start

# Run optimization
npm run optimize

# Run tests
npm test
```

## 📋 Available Scripts

### Development Scripts

#### `npm run dev`
Starts the development server with hot reload and debugging features.

```bash
npm run dev
```

**Features:**
- Hot reload for CSS/JS changes
- Development logging
- Source maps enabled
- Error overlay
- Automatic browser refresh

**Output:**
```
🚀 Daniel Orji Portfolio Server
📍 Environment: development
🌐 Server running on: http://localhost:3000
⚡ Features: Compression, Security Headers, Optimized Caching
🛡️  Security: Helmet, CORS, CSP enabled
📊 Logging: Development mode
```

#### `npm start`
Starts the production server with optimizations enabled.

```bash
npm start
```

**Features:**
- Production optimizations
- Asset compression
- Security headers
- Performance monitoring
- Error logging

### Build & Optimization Scripts

#### `npm run optimize`
Optimizes CSS, images, and other assets for production.

```bash
npm run optimize
```

**What it does:**
- Minifies CSS files
- Optimizes images
- Generates optimized HTML
- Creates compressed assets
- Updates file references

**Output files:**
- `*.opt.html` - Optimized HTML files
- `css/*.min.css` - Minified CSS files
- `images/optimized/` - Optimized images

#### `npm run build` (if available)
Creates a production build of the application.

```bash
npm run build
```

### Quality Assurance Scripts

#### `npm run lint`
Runs ESLint to check code quality and style.

```bash
npm run lint
```

**Checks:**
- JavaScript syntax errors
- Code style consistency
- Best practices
- Security issues
- Performance anti-patterns

#### `npm run lint:fix`
Automatically fixes linting issues where possible.

```bash
npm run lint:fix
```

#### `npm run validate`
Validates HTML files for compliance and accessibility.

```bash
npm run validate
```

**Validates:**
- HTML5 compliance
- Accessibility standards
- SEO best practices
- Performance guidelines

#### `npm test`
Runs the complete test suite.

```bash
npm test
```

**Includes:**
- ESLint checks
- HTML validation
- API endpoint tests
- Performance tests

### Utility Scripts

#### `npm run clean`
Cleans build artifacts and temporary files.

```bash
npm run clean
```

**Removes:**
- `*.opt.html` files
- `node_modules/.cache/`
- Temporary build files
- Log files

## 🌐 Server Configuration

### Environment Modes

#### Development Mode
```bash
NODE_ENV=development npm run dev
```

**Features:**
- Detailed error messages
- Source maps
- Hot reload
- Debug logging
- Development middleware

#### Production Mode
```bash
NODE_ENV=production npm start
```

**Features:**
- Optimized performance
- Compressed assets
- Security headers
- Error logging
- Production middleware

### Port Configuration

#### Default Port (3000)
```bash
npm run dev
# Server runs on http://localhost:3000
```

#### Custom Port
```bash
PORT=8080 npm run dev
# Server runs on http://localhost:8080
```

#### Environment Variable
```bash
# In .env file
PORT=3001

# Or export
export PORT=3001
npm run dev
```

### Host Configuration

#### Localhost Only (Default)
```bash
HOST=localhost npm run dev
# Accessible only from local machine
```

#### All Interfaces
```bash
HOST=0.0.0.0 npm run dev
# Accessible from network
```

## 🔧 Development Workflow

### 1. Starting Development

```bash
# Navigate to project directory
cd Black-And-White-master

# Install/update dependencies
npm install

# Start development server
npm run dev
```

### 2. Making Changes

#### CSS Changes
- Edit files in `css/` directory
- Changes are automatically reloaded
- Use design system classes from `css/design-system.css`

#### HTML Changes
- Edit `.html` files in root directory
- Refresh browser to see changes
- Use `page-template.html` for new pages

#### JavaScript Changes
- Edit files in `js/` directory
- Changes trigger automatic reload
- Follow ESLint rules for consistency

### 3. Testing Changes

```bash
# Run linting
npm run lint

# Validate HTML
npm run validate

# Run full test suite
npm test

# Test specific functionality
curl http://localhost:3000/api/health
```

### 4. Optimizing for Production

```bash
# Optimize assets
npm run optimize

# Test production build
NODE_ENV=production npm start

# Verify optimization
ls -la *.opt.html
```

## 📊 Monitoring & Debugging

### Server Logs

#### Development Logs
```bash
# Start with debug logging
DEBUG=* npm run dev

# View logs in real-time
tail -f server.log
```

#### Production Logs
```bash
# Start with production logging
NODE_ENV=production npm start

# View error logs
tail -f error.log
```

### Health Monitoring

#### Health Check Endpoint
```bash
curl http://localhost:3000/api/health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0",
  "environment": "development"
}
```

#### Portfolio Stats
```bash
curl http://localhost:3000/api/portfolio/stats
```

### Performance Monitoring

#### Server Performance
```bash
# Monitor server resources
top -p $(pgrep node)

# Check memory usage
ps aux | grep node
```

#### Browser Performance
- Open Developer Tools (F12)
- Go to Performance tab
- Record page load
- Analyze metrics

### Debugging

#### Node.js Debugging
```bash
# Start with inspector
node --inspect server.js

# Connect with Chrome DevTools
# Open chrome://inspect in Chrome
```

#### Browser Debugging
- Open Developer Tools (F12)
- Check Console for errors
- Use Network tab for API calls
- Use Elements tab for DOM inspection

## 🔄 Development Best Practices

### Code Quality

#### Before Committing
```bash
# Run all checks
npm test

# Fix linting issues
npm run lint:fix

# Validate HTML
npm run validate

# Optimize assets
npm run optimize
```

#### Git Workflow
```bash
# Create feature branch
git checkout -b feature/your-feature

# Make changes and test
npm test

# Commit changes
git add .
git commit -m "Add your feature description"

# Push to remote
git push origin feature/your-feature
```

### Performance Optimization

#### CSS Optimization
- Use design system classes
- Minimize custom CSS
- Avoid inline styles
- Use CSS variables for theming

#### JavaScript Optimization
- Follow ESLint rules
- Minimize DOM manipulation
- Use event delegation
- Avoid memory leaks

#### Image Optimization
```bash
# Optimize images
npm run optimize

# Check optimized images
ls -la images/optimized/
```

### Security Best Practices

#### Environment Variables
```bash
# Never commit sensitive data
echo ".env" >> .gitignore

# Use environment variables for secrets
export API_KEY=your-secret-key
```

#### Dependencies
```bash
# Check for vulnerabilities
npm audit

# Fix vulnerabilities
npm audit fix

# Update dependencies
npm update
```

## 🚀 Deployment Preparation

### Pre-deployment Checklist

- [ ] All tests pass (`npm test`)
- [ ] Code is linted (`npm run lint`)
- [ ] HTML is validated (`npm run validate`)
- [ ] Assets are optimized (`npm run optimize`)
- [ ] Environment variables are set
- [ ] Security headers are configured
- [ ] Performance is acceptable
- [ ] All pages load correctly
- [ ] API endpoints work
- [ ] Dark mode functions properly

### Production Build

```bash
# Create production build
NODE_ENV=production npm run optimize

# Test production server locally
NODE_ENV=production npm start

# Verify all functionality
curl http://localhost:3000/api/health
```

### Process Management

#### Using PM2
```bash
# Install PM2 globally
npm install -g pm2

# Start application
pm2 start server.js --name "daniel-portfolio"

# Monitor application
pm2 monit

# View logs
pm2 logs daniel-portfolio

# Restart application
pm2 restart daniel-portfolio
```

#### Using systemd (Linux)
```bash
# Create service file
sudo nano /etc/systemd/system/daniel-portfolio.service

# Enable and start service
sudo systemctl enable daniel-portfolio
sudo systemctl start daniel-portfolio

# Check status
sudo systemctl status daniel-portfolio
```

## 📞 Support & Troubleshooting

### Common Issues

#### Server Won't Start
```bash
# Check port availability
lsof -i :3000

# Check Node.js version
node --version

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

#### Performance Issues
```bash
# Increase memory limit
node --max-old-space-size=4096 server.js

# Check system resources
htop
```

#### Build Failures
```bash
# Clear npm cache
npm cache clean --force

# Update npm
npm install -g npm@latest

# Check disk space
df -h
```

### Getting Help

1. **Check logs** for error messages
2. **Review documentation** in project files
3. **Test in isolation** to identify issues
4. **Check system requirements** and compatibility
5. **Contact maintainer** for specific problems

---

*Ready to develop! The Daniel Orji Linux Kernel Engineer portfolio is now set up for development and production use.*
