/*
Portfolio Enhancement CSS for Daniel <PERSON>ji
Linux Kernel Engineer <PERSON><PERSON><PERSON>
Based on Black & White Theme
*/

/**
 * 0.5 - Accessibility Improvements
 */
.skip-nav {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--kernel-dark);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 10000;
  transition: top 0.3s;
}

.skip-nav:focus {
  top: 6px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus indicators for better accessibility */
*:focus {
  outline: 2px solid var(--rust-accent);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #000000;
    --text-secondary: #333333;
    --bg-white: #ffffff;
    --border-light: #000000;
  }

  [data-theme="dark"] {
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --bg-white: #000000;
    --border-light: #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .skills-scroll-content {
    animation: none;
  }
}

/**
 * 1.0 - Portfolio Color Variables & Theme System
 */
:root {
  /* Light Mode Colors */
  --kernel-dark: #1a365d;       /* Deep blue (Linux theme) */
  --rust-accent: #e14c38;       /* Rust orange */
  --cloud-light: #edf2f7;       /* Cloud/Infrastructure palette */
  --terminal-green: #00ff00;    /* Terminal green accent */
  --code-gray: #2d3748;         /* Code background */
  --text-primary: #333;         /* Primary text */
  --text-secondary: #666;       /* Secondary text */
  --border-light: #e2e8f0;      /* Light borders */
  --bg-white: #ffffff;          /* White background */
  --shadow-soft: 0 4px 6px rgba(0, 0, 0, 0.1);

  /* Theme transition */
  --theme-transition: all 0.3s ease;
}

/* Dark Mode Color Variables */
[data-theme="dark"] {
  --kernel-dark: #2563eb;       /* Brighter blue for dark mode */
  --kernel-darker: #1e40af;     /* Darker variant */
  --rust-accent: #f97316;       /* Vibrant orange for dark mode */
  --cloud-light: #374151;       /* Dark cloud/Infrastructure palette */
  --terminal-green: #10b981;    /* Softer terminal green */
  --code-gray: #1f2937;         /* Darker code background */
  --text-primary: #f9fafb;      /* Light text */
  --text-secondary: #d1d5db;    /* Secondary light text */
  --border-light: #374151;      /* Dark borders */
  --bg-white: #111827;          /* Dark background */
  --bg-secondary: #1f2937;      /* Secondary dark background */
  --shadow-soft: 0 4px 6px rgba(0, 0, 0, 0.3);
  --shadow-dark: 0 4px 6px rgba(0, 0, 0, 0.5);
}

/* Base theme transitions */
* {
  transition: background-color var(--theme-transition),
              color var(--theme-transition),
              border-color var(--theme-transition),
              box-shadow var(--theme-transition);
}

/* Dark mode body styles */
[data-theme="dark"] body {
  background-color: var(--bg-white);
  color: var(--text-primary);
}

[data-theme="dark"] .container {
  background-color: transparent;
}

/**
 * 1.1 - Dark Mode Toggle Button (Header Integration)
 */
#header-theme-toggle {
  float: right;
  margin-top: 15px;
  margin-right: 0;
  z-index: 1000;
  position: relative;
}

/* Ensure visibility in all contexts */
[data-theme="dark"] #header-theme-toggle {
  z-index: 1001;
}

.theme-toggle {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--border-light);
  padding: 12px;
  border-radius: 50%;
  transition: var(--theme-transition);
  width: 44px;
  height: 44px;
  min-width: 44px;
  min-height: 44px;
}

.theme-toggle:hover {
  background-color: var(--cloud-light);
  transform: scale(1.1);
  border-color: var(--kernel-dark);
}

.theme-toggle:focus {
  outline: 2px solid var(--rust-accent);
  outline-offset: 2px;
}

.theme-toggle-icon {
  width: 20px;
  height: 20px;
  color: var(--text-primary);
  transition: var(--theme-transition);
}

/* Sun icon for light mode */
.theme-toggle .sun-icon {
  display: block;
}

.theme-toggle .moon-icon {
  display: none;
}

/* Moon icon for dark mode */
[data-theme="dark"] .theme-toggle .sun-icon {
  display: none;
}

[data-theme="dark"] .theme-toggle .moon-icon {
  display: block;
}

/* Dark Mode Theme Toggle Styling */
[data-theme="dark"] .theme-toggle {
  background: rgba(249, 250, 251, 0.1);
  border-color: var(--border-light);
  backdrop-filter: blur(10px);
}

[data-theme="dark"] .theme-toggle:hover {
  background: var(--cloud-light);
  border-color: var(--rust-accent);
  transform: scale(1.1);
}

[data-theme="dark"] .theme-toggle:focus {
  outline: 2px solid var(--rust-accent);
  outline-offset: 2px;
  background: var(--cloud-light);
}

[data-theme="dark"] .theme-toggle-icon {
  color: var(--text-primary);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5));
}

[data-theme="dark"] .theme-toggle:hover .theme-toggle-icon {
  color: var(--rust-accent);
}

/* Additional contrast improvements for dark mode */
[data-theme="dark"] .theme-toggle:active {
  background: var(--kernel-dark);
  border-color: var(--rust-accent);
  transform: scale(0.95);
}

[data-theme="dark"] .theme-toggle:active .theme-toggle-icon {
  color: var(--bg-white);
}

/* High contrast mode specific improvements */
@media (prefers-contrast: high) {
  [data-theme="dark"] .theme-toggle {
    background: var(--bg-secondary);
    border: 2px solid var(--text-primary);
  }

  [data-theme="dark"] .theme-toggle:hover {
    background: var(--rust-accent);
    border-color: var(--text-primary);
  }

  [data-theme="dark"] .theme-toggle-icon {
    color: var(--text-primary);
    filter: none;
  }
}

/* Theme toggle integration with existing navigation */
.theme-toggle-container {
  display: flex;
  align-items: center;
  margin-left: 15px;
}

.navbar-nav .theme-toggle-container {
  margin-top: 0;
  margin-bottom: 0;
}

.navbar-nav .theme-toggle {
  margin: 0;
  padding: 10px;
  border-radius: 4px;
}

/* Mobile theme toggle */
@media (max-width: 768px) {
  .theme-toggle {
    margin-left: 10px;
    padding: 6px;
  }

  .theme-toggle-icon {
    width: 18px;
    height: 18px;
  }

  .theme-toggle-container {
    margin-left: 10px;
  }

  /* Dark mode mobile improvements */
  [data-theme="dark"] .theme-toggle {
    background: rgba(249, 250, 251, 0.15);
    border-width: 2px;
  }

  [data-theme="dark"] .theme-toggle:hover {
    background: var(--cloud-light);
    transform: scale(1.05);
  }
}

/**
 * 2.0 - Hero Section Styles
 */
.hero-section {
  background: linear-gradient(135deg, var(--bg-white) 0%, var(--cloud-light) 100%);
  padding: 80px 0;
  text-align: center;
  border-bottom: 2px solid var(--border-light);
  margin-bottom: 60px;
}

/* Dark mode hero section */
[data-theme="dark"] .hero-section {
  background: linear-gradient(135deg, var(--bg-white) 0%, var(--cloud-light) 100%);
  border-bottom-color: var(--border-light);
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 300;
  color: var(--kernel-dark);
  margin-bottom: 20px;
  font-family: 'Lato', sans-serif;
}

.hero-subtitle {
  font-size: 1.8rem;
  color: var(--rust-accent);
  margin-bottom: 25px;
  font-weight: 400;
}

.hero-description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 800px;
  margin: 0 auto 40px;
  line-height: 1.6;
}

/**
 * 3.0 - Animated Skills Scroller
 */
.skills-scroller {
  background: var(--kernel-dark);
  color: white;
  padding: 15px 0;
  overflow: hidden;
  white-space: nowrap;
  margin: 40px 0;
  position: relative;
}

.skills-scroll-content {
  display: inline-block;
  animation: scroll-left 30s linear infinite;
  font-size: 1.1rem;
  font-weight: 300;
  letter-spacing: 2px;
}

@keyframes scroll-left {
  0% { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}

/**
 * 4.0 - Skills Matrix
 */
.skills-matrix {
  padding: 60px 0;
  background: var(--bg-white);
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  margin-top: 40px;
}

.skill-category {
  background: var(--bg-white);
  padding: 30px;
  border-radius: 8px;
  box-shadow: var(--shadow-soft);
  border: 1px solid var(--border-light);
  text-align: center;
}

/* Dark mode skill category */
[data-theme="dark"] .skill-category {
  background: var(--bg-secondary);
  border-color: var(--border-light);
  box-shadow: var(--shadow-dark);
}

.skill-category h3 {
  color: var(--kernel-dark);
  margin-bottom: 25px;
  font-size: 1.4rem;
  font-weight: 400;
}

/**
 * 5.0 - Technology Badges
 */
.tech-badges {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}

.badge {
  background: var(--kernel-dark);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 300;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.badge:hover {
  background: var(--rust-accent);
  transform: translateY(-2px);
  box-shadow: var(--shadow-soft);
}

.badge.rust {
  background: var(--rust-accent);
}

.badge.linux {
  background: var(--kernel-dark);
}

.badge.cloud {
  background: var(--code-gray);
}

/**
 * 6.0 - Skill Progress Bars
 */
.skill-item {
  margin-bottom: 25px;
  text-align: left;
}

.skill-name {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-weight: 400;
  color: var(--text-primary);
}

.skill-bar {
  height: 8px;
  background: var(--border-light);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.skill-progress {
  height: 100%;
  border-radius: 4px;
  transition: width 2s ease-in-out;
  position: relative;
}

.skill-progress.c-lang { 
  background: var(--kernel-dark); 
  width: 90%; 
}

.skill-progress.rust-lang { 
  background: var(--rust-accent); 
  width: 65%; 
}

.skill-progress.python { 
  background: var(--code-gray); 
  width: 80%; 
}

.skill-progress.linux { 
  background: var(--kernel-dark); 
  width: 85%; 
}

.skill-progress.cloud { 
  background: var(--code-gray); 
  width: 75%; 
}

/**
 * 7.0 - Featured Projects Grid
 */
.featured-projects {
  padding: 60px 0;
  background: var(--cloud-light);
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  color: var(--kernel-dark);
  margin-bottom: 50px;
  font-weight: 300;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.project-card {
  background: var(--bg-white);
  border-radius: 8px;
  padding: 30px;
  box-shadow: var(--shadow-soft);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
  text-align: left;
}

/* Dark mode project card */
[data-theme="dark"] .project-card {
  background: var(--bg-secondary);
  border-color: var(--border-light);
  box-shadow: var(--shadow-dark);
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.project-title {
  color: var(--kernel-dark);
  font-size: 1.4rem;
  margin-bottom: 15px;
  font-weight: 400;
}

.project-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 20px;
}

.project-links {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.btn-project {
  padding: 10px 20px;
  border: 2px solid var(--kernel-dark);
  color: var(--kernel-dark);
  text-decoration: none;
  border-radius: 4px;
  font-weight: 400;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.btn-project:hover {
  background: var(--kernel-dark);
  color: white;
  text-decoration: none;
}

.btn-project.primary {
  background: var(--kernel-dark);
  color: white;
}

.btn-project.primary:hover {
  background: var(--rust-accent);
  border-color: var(--rust-accent);
}

/**
 * 8.0 - Professional Enhancements
 */
.cta-section {
  text-align: center;
  padding: 60px 0;
  background: var(--bg-white);
}

.cta-title {
  font-size: 2rem;
  color: var(--kernel-dark);
  margin-bottom: 20px;
  font-weight: 300;
}

.cta-description {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin-bottom: 30px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.btn-cta {
  display: inline-block;
  padding: 15px 30px;
  background: var(--rust-accent);
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 400;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  border: 2px solid var(--rust-accent);
}

.btn-cta:hover {
  background: var(--kernel-dark);
  border-color: var(--kernel-dark);
  color: white;
  text-decoration: none;
  transform: translateY(-2px);
  box-shadow: var(--shadow-soft);
}

/**
 * 9.0 - Responsive Design
 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.4rem;
  }

  .hero-description {
    font-size: 1rem;
    padding: 0 20px;
  }

  .skills-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .project-links {
    flex-direction: column;
    gap: 10px;
  }

  .btn-project {
    text-align: center;
  }

  .section-title {
    font-size: 2rem;
  }

  .skills-scroll-content {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 60px 0;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .skill-category {
    padding: 20px;
  }

  .project-card {
    padding: 20px;
  }

  .featured-projects,
  .skills-matrix,
  .cta-section {
    padding: 40px 0;
  }
}

/**
 * 10.0 - Animation Utilities
 */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.slide-in-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: all 0.6s ease;
}

.slide-in-left.visible {
  opacity: 1;
  transform: translateX(0);
}

.slide-in-right {
  opacity: 0;
  transform: translateX(50px);
  transition: all 0.6s ease;
}

.slide-in-right.visible {
  opacity: 1;
  transform: translateX(0);
}

/**
 * 11.0 - Comprehensive Dark Mode Styles
 */

/* Navigation and Header Dark Mode */
[data-theme="dark"] .main-nav .navbar-nav > li > a {
  color: var(--text-primary);
}

[data-theme="dark"] .main-nav .navbar-nav > li > a:hover {
  color: var(--rust-accent);
}

[data-theme="dark"] .logo h1 a {
  color: var(--text-primary);
}

[data-theme="dark"] .navbar-toggle .ion-navicon {
  color: var(--text-primary);
}

/* Content and Typography Dark Mode */
[data-theme="dark"] .content-body {
  background-color: var(--bg-white);
}

[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6 {
  color: var(--text-primary);
}

[data-theme="dark"] p {
  color: var(--text-secondary);
}

/* Form Elements Dark Mode */
[data-theme="dark"] .form-control {
  background-color: var(--bg-secondary);
  border-color: var(--border-light);
  color: var(--text-primary);
}

[data-theme="dark"] .form-control:focus {
  background-color: var(--bg-secondary);
  border-color: var(--kernel-dark);
  color: var(--text-primary);
  box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

[data-theme="dark"] .form-control::placeholder {
  color: var(--text-secondary);
}

/* Button Dark Mode Styles - Enhanced for Design System Integration */
[data-theme="dark"] .btn-project {
  border-color: var(--kernel-dark);
  color: var(--kernel-dark);
  background-color: transparent;
}

[data-theme="dark"] .btn-project:hover {
  background-color: var(--kernel-dark);
  color: var(--text-inverse);
  text-decoration: none;
}

[data-theme="dark"] .btn-project.primary {
  background-color: var(--kernel-dark);
  color: var(--text-inverse);
}

[data-theme="dark"] .btn-project.primary:hover {
  background-color: var(--rust-accent);
  border-color: var(--rust-accent);
  color: var(--text-inverse);
}

[data-theme="dark"] .btn-cta {
  background-color: var(--rust-accent);
  border-color: var(--rust-accent);
  color: var(--text-inverse);
}

[data-theme="dark"] .btn-cta:hover {
  background-color: var(--kernel-dark);
  border-color: var(--kernel-dark);
  color: var(--text-inverse);
  text-decoration: none;
}

/* Design System Button Integration for Dark Mode */
[data-theme="dark"] .btn-primary {
  background-color: var(--rust-accent);
  border-color: var(--rust-accent);
  color: var(--text-inverse);
}

[data-theme="dark"] .btn-primary:hover,
[data-theme="dark"] .btn-primary:focus {
  background-color: var(--kernel-dark);
  border-color: var(--kernel-dark);
  color: var(--text-inverse);
  text-decoration: none;
  transform: translateY(-2px);
}

[data-theme="dark"] .btn-secondary {
  background-color: transparent;
  border-color: var(--kernel-dark);
  color: var(--kernel-dark);
}

[data-theme="dark"] .btn-secondary:hover,
[data-theme="dark"] .btn-secondary:focus {
  background-color: var(--kernel-dark);
  border-color: var(--kernel-dark);
  color: var(--text-inverse);
  text-decoration: none;
}

/* Footer Dark Mode */
[data-theme="dark"] #site-footer {
  background-color: var(--bg-secondary);
  border-top: 1px solid var(--border-light);
}

[data-theme="dark"] .copyright {
  color: var(--text-secondary);
}

/* Enhanced Text Element Dark Mode Coverage */
[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6 {
  color: var(--text-primary);
}

[data-theme="dark"] p {
  color: var(--text-primary);
}

[data-theme="dark"] .navbar-nav > li > a {
  color: var(--text-primary);
}

[data-theme="dark"] .navbar-nav > li > a:hover,
[data-theme="dark"] .navbar-nav > li > a:focus {
  color: var(--rust-accent);
  background-color: transparent;
}

[data-theme="dark"] .navbar-toggle {
  border-color: var(--border-light);
}

[data-theme="dark"] .navbar-toggle:hover,
[data-theme="dark"] .navbar-toggle:focus {
  background-color: var(--bg-secondary);
  border-color: var(--rust-accent);
}

/* Mobile Overlay Dark Mode */
[data-theme="dark"] .overlay {
  background-color: rgba(17, 24, 39, 0.95);
}

[data-theme="dark"] .overlay nav ul li a {
  color: var(--text-primary);
}

[data-theme="dark"] .overlay nav ul li a:hover {
  color: var(--rust-accent);
}

[data-theme="dark"] .overlay-close {
  color: var(--text-primary);
}

/* Skills and Progress Bars Dark Mode */
[data-theme="dark"] .skill-bar {
  background-color: var(--cloud-light);
}

[data-theme="dark"] .skill-name {
  color: var(--text-primary);
}

/* Badge Dark Mode Enhancements */
[data-theme="dark"] .badge {
  background-color: var(--kernel-dark);
  color: var(--bg-white);
}

[data-theme="dark"] .badge:hover {
  background-color: var(--rust-accent);
  transform: translateY(-2px);
  box-shadow: var(--shadow-dark);
}

[data-theme="dark"] .badge.rust {
  background-color: var(--rust-accent);
}

[data-theme="dark"] .badge.linux {
  background-color: var(--kernel-dark);
}

[data-theme="dark"] .badge.cloud {
  background-color: var(--code-gray);
}

/* Enhanced Dark Mode Accessibility */
[data-theme="dark"] .hero-title {
  color: var(--text-primary);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .hero-subtitle {
  color: var(--rust-accent);
}

[data-theme="dark"] .hero-description {
  color: var(--text-secondary);
}

[data-theme="dark"] .section-title {
  color: var(--text-primary);
}

[data-theme="dark"] .project-title {
  color: var(--text-primary);
}

[data-theme="dark"] .project-description {
  color: var(--text-secondary);
}

[data-theme="dark"] .cta-title {
  color: var(--text-primary);
}

[data-theme="dark"] .cta-description {
  color: var(--text-secondary);
}

/* Dark Mode Scrollbar Styling */
[data-theme="dark"] ::-webkit-scrollbar {
  width: 8px;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
  background: var(--bg-white);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: var(--border-light);
  border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: var(--kernel-dark);
}

/* Additional Dark Mode Enhancements */

/* Header and Navigation Improvements */
[data-theme="dark"] .header {
  background-color: var(--bg-white);
  border-bottom: 1px solid var(--border-light);
}

[data-theme="dark"] .navbar {
  background-color: transparent;
}

[data-theme="dark"] .navbar-collapse {
  border-color: var(--border-light);
}

/* Content Area Improvements */
[data-theme="dark"] .content-area {
  background-color: var(--bg-white);
}

[data-theme="dark"] .main-content {
  background-color: var(--bg-white);
}

/* Skills Scroller Dark Mode */
[data-theme="dark"] .skills-scroller {
  background: linear-gradient(90deg, var(--bg-secondary) 0%, var(--cloud-light) 50%, var(--bg-secondary) 100%);
}

[data-theme="dark"] .skills-scroll-content .badge {
  background-color: var(--kernel-dark);
  color: var(--bg-white);
  border: 1px solid var(--border-light);
}

/* Call-to-Action Section Dark Mode */
[data-theme="dark"] .cta-section {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--cloud-light) 100%);
  border-top: 1px solid var(--border-light);
}

/* Enhanced Focus States for Accessibility */
[data-theme="dark"] .theme-toggle:focus {
  outline: 2px solid var(--rust-accent);
  outline-offset: 2px;
}

[data-theme="dark"] .btn:focus {
  box-shadow: 0 0 0 0.2rem rgba(249, 115, 22, 0.25);
}

[data-theme="dark"] .form-control:focus {
  box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

/* Link Improvements */
[data-theme="dark"] a {
  color: var(--kernel-dark);
}

[data-theme="dark"] a:hover {
  color: var(--rust-accent);
}

/* Table Dark Mode (if any) */
[data-theme="dark"] .table {
  color: var(--text-primary);
}

[data-theme="dark"] .table th,
[data-theme="dark"] .table td {
  border-color: var(--border-light);
}

[data-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) {
  background-color: var(--bg-secondary);
}

/* Code Block Dark Mode */
[data-theme="dark"] code {
  background-color: var(--code-gray);
  color: var(--terminal-green);
  border: 1px solid var(--border-light);
}

[data-theme="dark"] pre {
  background-color: var(--code-gray);
  color: var(--text-primary);
  border: 1px solid var(--border-light);
}

/* Enhanced Animation Performance */
[data-theme="dark"] * {
  will-change: auto;
}

[data-theme="dark"] .theme-toggle {
  will-change: transform, background-color;
}

/* Print Styles for Dark Mode */
@media print {
  [data-theme="dark"] * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
}

/**
 * 12.0 - Enhanced Responsive Design
 */

/* Mobile First Approach - Base styles for mobile */
@media (max-width: 767px) {
  /* Header improvements */
  #header-theme-toggle {
    margin-top: 10px;
    margin-right: 10px;
  }

  .theme-toggle {
    width: 40px;
    height: 40px;
    padding: 8px;
  }

  .theme-toggle-icon {
    width: 16px;
    height: 16px;
  }

  /* Dark mode mobile theme toggle visibility */
  [data-theme="dark"] #header-theme-toggle {
    z-index: 1002;
  }

  [data-theme="dark"] .theme-toggle {
    background: rgba(249, 250, 251, 0.2);
    border: 2px solid var(--border-light);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  [data-theme="dark"] .theme-toggle:hover {
    background: var(--cloud-light);
    border-color: var(--rust-accent);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  }

  /* Hero section mobile optimization */
  .hero-section {
    padding: 40px 0;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
    margin-bottom: 10px;
  }

  .hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 15px;
  }

  .hero-description {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 20px;
  }

  .hero-actions {
    flex-direction: column;
    gap: 15px;
  }

  .hero-actions .btn-project {
    margin-left: 0 !important;
  }

  /* Skills section mobile */
  .skills-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .skill-category {
    padding: 20px;
  }

  /* Projects grid mobile */
  .projects-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .project-card {
    padding: 20px;
  }

  .project-links {
    flex-direction: column;
    gap: 10px;
  }

  /* Skills scroller mobile */
  .skills-scroller {
    margin: 20px 0;
    font-size: 0.9rem;
  }

  /* Touch targets - minimum 44px */
  .btn-cta,
  .btn-project,
  .theme-toggle,
  .navbar-toggle {
    min-height: 44px;
    min-width: 44px;
  }

  /* Navigation improvements */
  .navbar-toggle {
    padding: 10px;
    margin-right: 10px;
  }
}

/* Tablet styles */
@media (min-width: 768px) and (max-width: 1023px) {
  .skills-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }

  .projects-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }

  .hero-title {
    font-size: 3rem;
  }

  .hero-subtitle {
    font-size: 1.4rem;
  }
}

/* Large desktop styles */
@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }

  .hero-title {
    font-size: 4rem;
  }

  .hero-subtitle {
    font-size: 1.6rem;
  }

  .skills-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 35px;
  }

  .projects-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 35px;
  }
}

/* Ultra-wide screens */
@media (min-width: 1400px) {
  .container {
    max-width: 1320px;
  }

  .hero-section {
    padding: 100px 0;
  }

  .section-title {
    font-size: 2.5rem;
  }
}

/* Landscape mobile orientation */
@media (max-width: 767px) and (orientation: landscape) {
  .hero-section {
    padding: 30px 0;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .theme-toggle-icon,
  .badge,
  .btn-cta,
  .btn-project {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Enhanced theme toggle for high DPI */
  [data-theme="dark"] .theme-toggle {
    border-width: 1.5px;
  }

  [data-theme="dark"] .theme-toggle-icon {
    filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.7));
  }
}

/**
 * 13.0 - Theme Toggle Accessibility & Visibility Enhancements
 */

/* Ensure theme toggle is always visible and accessible */
.theme-toggle {
  /* Prevent the button from being hidden by other elements */
  isolation: isolate;
  /* Ensure it's always clickable */
  pointer-events: auto;
}

/* Force visibility in edge cases */
[data-theme="dark"] .theme-toggle {
  /* Ensure minimum contrast */
  min-height: 44px;
  min-width: 44px;
  /* Prevent transparency issues */
  opacity: 1 !important;
  visibility: visible !important;
}

/* Animation states for better UX */
.theme-toggle {
  animation: none;
}

.theme-toggle:hover {
  animation: pulse-subtle 0.3s ease-in-out;
}

@keyframes pulse-subtle {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1.1); }
}

/* Ensure proper stacking context */
#header-theme-toggle {
  /* Create new stacking context */
  transform: translateZ(0);
  /* Ensure it's above other elements */
  position: relative;
  z-index: 1000;
}

[data-theme="dark"] #header-theme-toggle {
  z-index: 1001;
}

/* Fallback styles for older browsers */
.no-js .theme-toggle {
  display: none;
}

/* Loading state protection */
.theme-toggle.loading {
  pointer-events: none;
  opacity: 0.7;
}

/* Theme transition protection */
.theme-transitioning .theme-toggle {
  pointer-events: none;
}
